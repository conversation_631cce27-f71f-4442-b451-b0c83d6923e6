{% extends "base.html.twig" %}

{% block javascripts %}
    {{ parent() }}
    {{ vite_entry_script_tags('webHost') }}
{% endblock %}

{% block body %}
    <div class="d-flex justify-content-end p-3">
        <a href="{{ path('app_webHosts_create') }}" class="btn btn-primary">Ajouter un hébergement</a>
    </div>
    <table class="table table-hover">
        <thead class="table-dark">
            <tr>
                <th scope="col" style="width: 1px;"></th>
                <th scope="col"></th>
                <th scope="col">Nom</th>
                <th scope="col">Type</th>
                <th scope="col">Environnement</th>
                <th scope="col">Dépôt Git</th>
                <th scope="col">Visibilité</th>
                <th scope="col">URL</th>
                <th scope="col">Santé</th>
                <th scope="col">SSL</th>
            </tr>
        </thead>
        <tbody>
        {% for webHost in webHosts %}
            <tr class="table-light" data-id="{{ webHost.id }}">
                <td>
                    {% if webHost.urls|length > 1 %}
                    <button
                            class="btn btn-sm btn-transparent"
                            type="button"
                            data-bs-toggle="collapse"
                            data-bs-target="[data-web-host-id='{{ webHost.id }}']"
                            aria-expanded="false"
                    >
                        <i class="fa-regular fa-square-plus" data-toggle></i>
                        <i class="fa-regular fa-square-minus" data-toggle></i>
                    </button>
                    {% endif %}
                </td>
                <td>
                    <a href="{{ path('app_webHosts_edit', {'id': webHost.id}) }}" class="text-dark text-decoration-none">
                        <i class="fa-regular fa-pen"></i>
                    </a>
                    <a href="{{ path('app_webHosts_delete', {'id': webHost.id}) }}" class="text-danger text-decoration-none" onclick="return confirm('Voulez-vous vraiment supprimer cet hébergement ?')">
                        <i class="fa-solid fa-trash-can"></i>
                    </a>
                </td>
                <td>{{ webHost.name }}</td>
                <td>
                    {% if webHost.configuration.getType() == "vm" %}
                        <i class="fa-solid fa-server" title="Machine virtuelle"></i>
                    {% elseif webHost.configuration.getType() == "swarm" %}
                        <a href="{{ path('app_front', {'search': webHost.configuration.stack}) }}">
                            <img src="{{ asset('images/docker.png') }}" alt="Docker Swarm" width="20px" height="20px" title="Docker Swarm" />
                        </a>
                    {% endif %}
                </td>
                <td>
                    {% if webHost.environnement is not null %}
                        {% if webHost.environnement.value == 'dev' %}
                            <span class="badge bg-light text-dark">développement</span>
                        {% elseif webHost.environnement.value == 'preprod' %}
                            <span class="badge bg-secondary">preproduction</span>
                        {% elseif webHost.environnement.value == 'prod' %}
                            <span class="badge bg-success">production</span>
                        {% endif %}
                    {% endif %}
                </td>
                <td>
                    {% if webHost.gitlabRemoteUrl is not null %}
                        {% if '**********************:' in webHost.gitlabRemoteUrl %}
                            {% set remote = webHost.gitlabRemoteUrl|replace({'**********************:': '', '.git': ''}) %}
                            <a target="_blank" class="d-flex align-items-center gap-2" href="{{ 'https://gitlab.alienor.net/' ~ remote }}" title="{{ remote ~ ':' ~ webHost.gitlabActiveBranch }}">
                                <img src="{{ asset('images/gitlab.png') }}" width="16px" height="16px" alt="Gitlab"> Gitlab
                            </a>
                        {% elseif 'ssh://<EMAIL>' in webHost.gitlabRemoteUrl or 'ssh://<EMAIL>' in webHost.gitlabRemoteUrl %}
                            <span title="{{ webHost.gitlabRemoteUrl ~ ':' ~ webHost.gitlabActiveBranch }}">Gitweb</span>
                        {% elseif 'https://gitlab.alienor.net/' in webHost.gitlabRemoteUrl %}
                            <a target="_blank" class="d-flex align-items-center gap-2" href="{{ webHost.gitlabRemoteUrl }}" title="{{ webHost.gitlabRemoteUrl ~ ':' ~ webHost.gitlabActiveBranch }}">
                                <img src="{{ asset('images/gitlab.png') }}" width="16px" height="16px" alt="Gitlab"> Gitlab
                            </a>
                        {% else %}
                            {{ webHost.gitlabRemoteUrl }}
                        {% endif %}
                    {% endif %}
                </td>
                <td>
                    {% if webHost.expectedVisibility is not null %}
                        {% if webHost.expectedVisibility.value == 'internal' %}
                            <span class="badge bg-secondary">interne</span>
                        {% elseif webHost.expectedVisibility.value == 'external' %}
                            <span class="badge bg-success">externe</span>
                        {% endif %}
                    {% endif %}
                </td>
                <td>
                    {% set maxUrls = 2 %}
                    {% for webHostUrl in webHost.urls|slice(0, maxUrls) %}
                        <a target="_blank" href="{{ webHostUrl.url }}">{{ webHostUrl.url }}</a><br>
                    {% endfor %}
                    {% if webHost.urls|length > maxUrls %}
                        {% set remainingUrls = webHost.urls|length - maxUrls %}
                        ... et {{ remainingUrls }} autre{{ remainingUrls > 1 ? 's' : '' }}
                    {% endif %}
                </td>
                <td>
                    {% set statuses = webHost.urls|filter(url => url.healthCheckReport.status is not null)|map(url => url.healthCheckReport.status.value) %}
                    {# regroupe les statuts #}
                    {% if statuses|length > 0 %}
                        {% if 'CRITICAL' in statuses %}
                            <span class="badge bg-white">
                                <i class="fa-solid fa-ban text-danger"></i>
                            </span>
                        {% elseif 'WARNING' in statuses %}
                            <span class="badge bg-white">
                                <i class="fa-solid fa-triangle-exclamation text-warning"></i>
                            </span>
                        {% else %}
                            <span class="badge bg-white">
                                <i class="fa-solid fa-circle-check text-success"></i>
                            </span>
                        {% endif %}
                    {% endif %}
                </td>
                <td>
                    {% set statuses = webHost.urls|map(url => url.sslCertificateReport.isValid) %}
                    {% if statuses|length > 0 %}
                        {% if false in statuses %}
                            <span class="badge bg-white">
                                <i class="fa-solid fa-ban text-danger"></i>
                            </span>
                        {% else %}
                            <span class="badge bg-white">
                                <i class="fa-solid fa-circle-check text-success"></i>
                            </span>
                        {% endif %}
                    {% endif %}
                </td>
            </tr>
            {% for webHostUrl in webHost.urls %}
                <tr class="table-secondary collapse" data-web-host-id="{{ webHost.id }}" data-url-id="{{ webHostUrl.id }}">
                    <td colspan="7"></td>
                    <td>
                        {% if webHostUrl.url is not null %}
                            <a target="_blank" href="{{ webHostUrl.url }}">{{ webHostUrl.url }}</a>
                        {% endif %}
                    </td>
                    <td>
                        {% if webHostUrl.healthCheckReport.status is not null %}
                            {% set status = webHostUrl.healthCheckReport.status.value %}
                            {% if 'WARNING' == status %}
                                <span class="badge bg-white">
                                    <i class="fa-solid fa-triangle-exclamation text-warning"></i>
                                </span>
                            {% elseif 'CRITICAL' == status %}
                                <span class="badge bg-white">
                                    <i class="fa-solid fa-ban text-danger"></i>
                                </span>
                            {% else %}
                                <span class="badge bg-white">
                                    <i class="fa-solid fa-circle-check text-success"></i>
                                </span>
                            {% endif %}
                        {% endif %}
                    </td>
                    <td>
                        {% if webHostUrl.sslCertificateReport.isValid is not null %}
                            {% if webHostUrl.sslCertificateReport.isValid %}
                                <span class="badge bg-white">
                                    <i class="fa-solid fa-circle-check text-success"></i>
                                </span>
                            {% else %}
                                <span class="badge bg-white">
                                    <i class="fa-solid fa-ban text-danger"></i>
                                </span>
                            {% endif %}
                        {% endif %}
                    </td>
                </tr>
            {% endfor %}
        {% endfor %}
        </tbody>
    </table>
{% endblock body %}