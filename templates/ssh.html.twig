<style>
    a {
        padding: 0.6rem 0.5rem 0.2rem;
        background: #0dcaf0;
        color: white;
        cursor: pointer;
        border-radius: 0.25rem;
        line-height: 1.5;
        font-weight: bold;
        font-family: sans-serif;
    }

    a:hover {
        background: #0dabce;
    }

    a.error {
        background: #ce6f04;
        border: 2px solid #ce6f04;
        cursor: default;
        font-size: 0.7rem;
    }

    a.error:hover {
        background: #ce6f04;
    }
</style>

{% if service.tasks|length and service.tasks[0].sshCommand is defined %}
    <a onclick="navigator.clipboard.writeText('{{ service.tasks[0].sshCommand }}');">
        <svg fill="white" width="18px" height="18px" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><!--! Font Awesome Pro 6.2.1 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2022 Fonticons, Inc. --><path d="M64 32C28.7 32 0 60.7 0 96v64c0 35.3 28.7 64 64 64H448c35.3 0 64-28.7 64-64V96c0-35.3-28.7-64-64-64H64zM344 152c-13.3 0-24-10.7-24-24s10.7-24 24-24s24 10.7 24 24s-10.7 24-24 24zm96-24c0 13.3-10.7 24-24 24s-24-10.7-24-24s10.7-24 24-24s24 10.7 24 24zM64 288c-35.3 0-64 28.7-64 64v64c0 35.3 28.7 64 64 64H448c35.3 0 64-28.7 64-64V352c0-35.3-28.7-64-64-64H64zM344 408c-13.3 0-24-10.7-24-24s10.7-24 24-24s24 10.7 24 24s-10.7 24-24 24zm104-24c0 13.3-10.7 24-24 24s-24-10.7-24-24s10.7-24 24-24s24 10.7 24 24z"/></svg>
    </a>
{% else %}
    <a class="error">ERREUR</a>
{% endif %}
