<?php

namespace App\Scheduler;

use App\Command\CheckWebHostsHealthCommand;
use App\Command\CheckWebHostsSSLCommand;
use App\Command\RefreshAllServicesCommand;
use Symfony\Component\Console\Messenger\RunCommandMessage;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Scheduler\Attribute\AsSchedule;
use Symfony\Component\Scheduler\RecurringMessage;
use Symfony\Component\Scheduler\Schedule;
use Symfony\Component\Scheduler\ScheduleProviderInterface;

#[AsSchedule('command')]
class CommandScheduler implements ScheduleProviderInterface
{
    public function __construct(
        #[Autowire('%env(bool:ENABLE_SCHEDULER)%')] private readonly bool $enabled,
    ) {
    }

    public function getSchedule(): Schedule
    {
        $schedule = $this->schedule ??= (new Schedule());

        if (!$this->enabled) {
            return $schedule;
        }

        return $schedule->with(
            // Refresh de la liste des services toutes les heures de 7h à 19h
            RecurringMessage::cron('0 7-19 * * 1-5', new RunCommandMessage(RefreshAllServicesCommand::getDefaultName())),
            // Refresh des détails des services tous les jours à 7h30
            RecurringMessage::cron('30 7 * * 1-5', new RunCommandMessage(RefreshAllServicesCommand::getDefaultName() . ' --with-details')),
            // HealthCheck de toutes les URLs des hébergements toutes les deux heures de 7h à 19h du lundi au vendredi
            RecurringMessage::cron('0 7-19/2 * * 1-5', new RunCommandMessage(CheckWebHostsHealthCommand::getDefaultName())),
            // Check de la validité des certificats SSL de toutes les URLs des hébergements tous les jours à 7h du lundi au vendredi
            RecurringMessage::cron('0 7 * * 1-5', new RunCommandMessage(CheckWebHostsSSLCommand::getDefaultName())),
        );
    }
}
