<?php

namespace App\Entity\WebHost\WebHostConfiguration;

use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
class VirtualMachineConfiguration extends WebHostConfiguration
{
    #[ORM\Column(type: 'string', nullable: true)]
    private ?string $ip = null;

    #[ORM\Column(type: 'string', nullable: true)]
    private ?string $location = null;

    public function getIp(): ?string
    {
        return $this->ip;
    }

    public function setIp(?string $ip): void
    {
        $this->ip = $ip;
    }

    public function getLocation(): ?string
    {
        return $this->location;
    }

    public function setLocation(?string $location): void
    {
        $this->location = $location;
    }

    public function getType(): string
    {
        return self::TYPE_VM;
    }
}
