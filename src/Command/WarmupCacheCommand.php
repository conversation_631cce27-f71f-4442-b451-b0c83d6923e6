<?php

namespace App\Command;

use App\Service\MercureUpdater;
use App\Service\SwarmpitAPI;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'app:refresh-services',
    description: 'Refresh les infos d\'un ou plusieurs services et envoi les infos aux front en temps réel',
)]
class WarmupCacheCommand extends BaseServicesCommand
{
    protected function configure(): void
    {
        $this
            ->addArgument('services', InputArgument::IS_ARRAY, 'Liste des services à refresh, au format "{serverName}:{serviceId}, séparés par des espaces"')
        ;
    }

    public function __construct(private readonly MercureUpdater $updater, private readonly SwarmpitAPI $api)
    {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $services = $this->getServicesFromArguments($input->getArgument('services'));

        $this->api->setNoCache(true);

        foreach ($services as $service) {
            try {
                $serverName = $service['serverName'];
                $serviceId = $service['serviceId'];

                $server = $this->api->getServer($serverName);
                $service = $this->api->getServiceAndTasksInfos($server, $serviceId, true);
                $formattedService = $this->api->formatService($server, $service);
                $this->updater->updateService($formattedService);
            } catch (\Exception $exception) {
                $this->updater->sendError($serviceId, $exception->getMessage());
            }
        }

        return Command::SUCCESS;
    }
}
