<?php

namespace App\Command;

use App\Entity\WebHost\WebHostUrl;
use App\Message\HealthCheckMessage;
use Doctrine\Persistence\ManagerRegistry;
use Doctrine\Persistence\ObjectManager;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Messenger\MessageBusInterface;

#[AsCommand(
    name: 'app:webHosts:health-check',
    description: 'Check de l\'état de santé des hébergements',
)]
class CheckWebHostsHealthCommand
{
    private readonly ObjectManager $entityManager;

    public function __construct(
        private readonly MessageBusInterface $bus,
        ManagerRegistry $doctrine,
    ) {
        $this->entityManager = $doctrine->getManager();
    }

    public function __invoke(OutputInterface $output): int
    {
        $output->writeln('Check de l\'état de santé des hébergements ...');

        $webHostUrls = $this->entityManager->getRepository(WebHostUrl::class)->findAll();
        $messageArguments = array_map(fn ($webHostUrl) => ['webHostUrlId' => $webHostUrl->getId()], $webHostUrls);

        $this->bus->dispatch(new HealthCheckMessage($messageArguments));

        return Command::SUCCESS;
    }
}
