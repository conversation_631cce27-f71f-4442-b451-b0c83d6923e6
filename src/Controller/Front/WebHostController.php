<?php

namespace App\Controller\Front;

use App\Entity\WebHost\WebHost;
use App\Form\CreateWebHostType;
use App\Form\DTO\CreateWebHostDTO;
use App\Form\DTO\EditWebHostDTO;
use App\Form\EditWebHostType;
use App\Message\CheckSSLCertificatesMessage;
use App\Message\HealthCheckMessage;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/webHosts', priority: 1)]
class WebHostController extends AbstractController
{
    #[Route('/', name: 'app_webHosts')]
    public function webHosts(ManagerRegistry $doctrine): Response
    {
        // TODO crééer une classe de Repository dédiée
        $webHosts = $doctrine->getManagerForClass(WebHost::class)->createQueryBuilder()
            ->select('w, u, c')
            ->from(WebHost::class, 'w')
            ->leftJoin('w.urls', 'u')
            ->leftJoin('w.configuration', 'c')
            ->orderBy('w.name', 'ASC')
            ->getQuery()
            ->getResult()
        ;

        return $this->render('webHosts/index.html.twig', [
            'webHosts' => $webHosts,
        ]);
    }

    #[Route('/create', name: 'app_webHosts_create')]
    public function create(Request $request, ManagerRegistry $doctrine): Response
    {
        $form = $this->createForm(CreateWebHostType::class);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            /** @var CreateWebHostDTO $dto */
            $dto = $form->getData();
            $webHost = $dto->toEntity();
            $entityManager = $doctrine->getManager();
            $entityManager->persist($webHost);
            $entityManager->flush();

            return $this->redirectToRoute('app_webHosts');
        }

        return $this->render('webHosts/create.html.twig', [
            'form' => $form->createView(),
        ]);
    }

    #[Route('/edit/{id}', name: 'app_webHosts_edit')]
    public function edit(Request $request, ManagerRegistry $doctrine, int $id): Response
    {
        $webHost = $doctrine->getRepository(WebHost::class)->find($id);
        $dto = EditWebHostDTO::fromEntity($webHost);
        $form = $this->createForm(EditWebHostType::class, $dto);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            /** @var EditWebHostDTO $dto */
            $dto = $form->getData();
            $webHost = $dto->toEntity($webHost);
            $entityManager = $doctrine->getManager();
            $entityManager->persist($webHost);
            $entityManager->flush();

            return $this->redirectToRoute('app_webHosts');
        }

        return $this->render('webHosts/edit.html.twig', [
            'form' => $form->createView(),
        ]);
    }

    #[Route('/delete/{id}', name: 'app_webHosts_delete')]
    public function delete(Request $request, ManagerRegistry $doctrine, int $id): Response
    {
        $webHost = $doctrine->getRepository(WebHost::class)->find($id);
        $entityManager = $doctrine->getManager();
        $entityManager->remove($webHost);
        $entityManager->flush();

        return $this->redirectToRoute('app_webHosts');
    }

    #[Route('/healthcheck/{webHostId}', name: 'app_webHosts_healthcheck')]
    public function healthcheck(string $webHostId, MessageBusInterface $bus): JsonResponse
    {
        $bus->dispatch(new HealthCheckMessage([
            ['webHostId' => $webHostId],
        ]));

        return $this->json([
            'status' => 'OK',
        ]);
    }

    #[Route('/check-ssl-certificate/{webHostId}', name: 'app_webHosts_check_ssl_certificate')]
    public function checkSSLCertificate(string $webHostId, MessageBusInterface $bus): JsonResponse
    {
        $bus->dispatch(new CheckSSLCertificatesMessage([
            ['webHostId' => $webHostId],
        ]));

        return $this->json([
            'status' => 'OK',
        ]);
    }
}
