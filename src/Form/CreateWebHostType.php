<?php

namespace App\Form;

use App\Entity\WebHost\WebHostEnvironnement;
use App\Form\DTO\CreateWebHostDTO;
use App\Model\WebsiteVisibility;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\CallbackTransformer;
use Symfony\Component\Form\Extension\Core\Type\EnumType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class CreateWebHostType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('name', TextType::class, [
                'label' => 'Nom',
                'required' => false,
            ])
            ->add('urls', TextareaType::class, [
                'label' => 'URLs',
                'attr' => [
                    'placeholder' => 'https://www.example.com; https://www.example.com/fr',
                ],
                'help' => 'Séparez les URLs par un point-virgule',
            ])
            ->add('expectedVisibility', EnumType::class, [
                'label' => 'Visibilité attendue',
                'class' => WebsiteVisibility::class,
                'placeholder' => '',
            ])
            ->add('environnement', EnumType::class, [
                'label' => 'Environnement',
                'class' => WebHostEnvironnement::class,
                'placeholder' => '',
            ])
        ;

        $builder->get('urls')
            ->addModelTransformer(new CallbackTransformer(
                function ($tagsAsArray): ?string {
                    // transform the array to a string
                    return null !== $tagsAsArray ? implode(';', $tagsAsArray) : null;
                },
                function ($tagsAsString): array {
                    // transform the string back to an array
                    return explode(';', $tagsAsString);
                }
            ))
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => CreateWebHostDTO::class,
        ]);
    }
}
