<?php

namespace App\Form\DTO;

use App\Entity\WebHost\WebHost;
use App\Entity\WebHost\WebHostEnvironnement;
use App\Entity\WebHost\WebHostUrl;
use App\Model\WebsiteVisibility;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Context\ExecutionContextInterface;

class CreateWebHostDTO
{
    public function __construct(
        public ?string $name,
        #[Assert\NotBlank]
        public array $urls = [],
        #[Assert\NotBlank]
        public ?WebsiteVisibility $expectedVisibility = null,
        #[Assert\NotBlank]
        public ?WebHostEnvironnement $environnement = null,
    ) {
    }

    #[Assert\Callback]
    public function validateUrls(ExecutionContextInterface $context, mixed $payload): void
    {
        if (count($this->urls) < 1 || empty($this->urls[0])) {
            $context->buildViolation('Vous devez spécifier au moins une URL')
                ->atPath('urls')
                ->addViolation();

            return;
        }

        foreach ($this->urls as $url) {
            if (!filter_var($url, FILTER_VALIDATE_URL)) {
                $context->buildViolation('Toutes les URL doivent être valides')
                    ->atPath('urls')
                    ->addViolation();
            }
        }
    }

    public function toEntity(): WebHost
    {
        $webHost = new WebHost();
        $webHost->setName($this->name);
        foreach ($this->urls as $url) {
            $webHostUrl = new WebHostUrl();
            $webHostUrl->setUrl($url);
            $webHost->addUrl($webHostUrl);
        }
        $webHost->setExpectedVisibility($this->expectedVisibility);
        $webHost->setEnvironnement($this->environnement);

        return $webHost;
    }
}
