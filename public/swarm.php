<?php

$projectPath = '/var/www/html';
$rootPath = '/var/www/_anetversion';
$swarmCheckerPath = $rootPath . '/swarm-checker.phar';

if (file_exists($swarmCheckerPath)) {
    $swarmCheckerCommand = sprintf('/usr/local/bin/php %s', $swarmCheckerPath);
    $swarmCheckerResult = shell_exec($swarmCheckerCommand);
    var_dump($swarmCheckerResult);
    exit;
    echo $swarmCheckerResult;
} else {
    echo json_encode([
        'status' => 'error',
    ]);
}

header('Content-Type: application/json; charset=utf-8');
