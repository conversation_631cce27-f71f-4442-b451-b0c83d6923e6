
###> symfony/framework-bundle ###
/.env.local
/.env.local.php
/.env.*.local
/config/secrets/prod/prod.decrypt.private.php
/public/bundles/
/var/
/vendor/
###< symfony/framework-bundle ###

###> custom ###
.idea
.docker/certs
/config/swarmpit.yaml
data.db
###< custom ###

###> swarm-checker ###
/swarm-checker/dist/swarm-checker.phar
/swarm-checker/vendor
###< custom ###

###> symfony/webpack-encore-bundle ###
/node_modules/
/public/build/
npm-debug.log
yarn-error.log
###< symfony/webpack-encore-bundle ###

###> friendsofphp/php-cs-fixer ###
/.php-cs-fixer.php
/.php-cs-fixer.cache
###< friendsofphp/php-cs-fixer ###

###> pentatrion/vite-bundle ###
/node_modules/
/public/build/
###< pentatrion/vite-bundle ###

###> phpstan/phpstan ###
phpstan.neon
###< phpstan/phpstan ###
