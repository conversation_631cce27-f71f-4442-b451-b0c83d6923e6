{"doctrine/annotations": {"version": "1.13", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.10", "ref": "64d8583af5ea57b7afa4aba4b159907f3a148b05"}}, "doctrine/deprecations": {"version": "1.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "87424683adc81d7dc305eefec1fced883084aab9"}}, "doctrine/doctrine-bundle": {"version": "2.13", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.13", "ref": "8d96c0b51591ffc26794d865ba3ee7d193438a83"}, "files": ["config/packages/doctrine.yaml", "src/Entity/.gitignore", "src/Repository/.gitignore"]}, "doctrine/doctrine-migrations-bundle": {"version": "3.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.1", "ref": "1d01ec03c6ecbd67c3375c5478c9a423ae5d6a33"}, "files": ["config/packages/doctrine_migrations.yaml", "migrations/.gitignore"]}, "friendsofphp/php-cs-fixer": {"version": "3.11", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.0", "ref": "be2103eb4a20942e28a6dd87736669b757132435"}, "files": [".php-cs-fixer.dist.php"]}, "knplabs/packagist-api": {"version": "1.7", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.6", "ref": "6db69661915a16528535b90973e02593cc3b84e3"}}, "pentatrion/vite-bundle": {"version": "8.1", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "6.5", "ref": "3a6673f248f8fc1dd364dadfef4c5b381d1efab6"}}, "phpstan/phpstan": {"version": "2.1", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.0", "ref": "5e490cc197fb6bb1ae22e5abbc531ddc633b6767"}, "files": ["phpstan.dist.neon"]}, "symfony/apache-pack": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.0", "ref": "9d254a22efca7264203eea98b866f16f944b2f09"}, "files": ["public/.htaccess"]}, "symfony/console": {"version": "6.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "da0c8be8157600ad34f10ff0c9cc91232522e047"}, "files": ["bin/console"]}, "symfony/debug-bundle": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "5aa8aa48234c8eb6dbdd7b3cd5d791485d2cec4b"}, "files": ["config/packages/debug.yaml"]}, "symfony/flex": {"version": "2.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "146251ae39e06a95be0fe3d13c807bcf3938b172"}, "files": [".env"]}, "symfony/form": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.2", "ref": "7d86a6723f4a623f59e2bf966b6aad2fc461d36b"}, "files": ["config/packages/csrf.yaml"]}, "symfony/framework-bundle": {"version": "6.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.4", "ref": "3cd216a4d007b78d8554d44a5b1c0a446dab24fb"}, "files": ["config/packages/cache.yaml", "config/packages/framework.yaml", "config/preload.php", "config/routes/framework.yaml", "config/services.yaml", "public/index.php", "src/Controller/.gitignore", "src/Kernel.php"]}, "symfony/lock": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.2", "ref": "8e937ff2b4735d110af1770f242c1107fdab4c8e"}, "files": ["config/packages/lock.yaml"]}, "symfony/maker-bundle": {"version": "1.45", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "fadbfe33303a76e25cb63401050439aa9b1a9c7f"}}, "symfony/mercure-bundle": {"version": "0.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "0.3", "ref": "7c7e63c36530052a174f28a6be4e451c4709be83"}, "files": ["config/packages/mercure.yaml"]}, "symfony/messenger": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.0", "ref": "ba1ac4e919baba5644d31b57a3284d6ba12d52ee"}, "files": ["config/packages/messenger.yaml"]}, "symfony/monolog-bundle": {"version": "3.10", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.7", "ref": "aff23899c4440dd995907613c1dd709b6f59503f"}, "files": ["config/packages/monolog.yaml"]}, "symfony/property-info": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.3", "ref": "dae70df71978ae9226ae915ffd5fad817f5ca1f7"}, "files": ["config/packages/property_info.yaml"]}, "symfony/routing": {"version": "6.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.1", "ref": "a44010c0d06989bd4f154aa07d2542d47caf5b83"}, "files": ["config/packages/routing.yaml", "config/routes.yaml"]}, "symfony/scheduler": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.2", "ref": "caea3c928ee9e1b21288fd76aef36f16ea355515"}, "files": ["src/Schedule.php"]}, "symfony/twig-bundle": {"version": "6.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.4", "ref": "bb2178c57eee79e6be0b297aa96fc0c0def81387"}, "files": ["config/packages/twig.yaml", "templates/base.html.twig"]}, "symfony/validator": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "8c1c4e28d26a124b0bb273f537ca8ce443472bfd"}, "files": ["config/packages/validator.yaml"]}, "symfony/web-profiler-bundle": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.1", "ref": "8b51135b84f4266e3b4c8a6dc23c9d1e32e543b7"}, "files": ["config/packages/web_profiler.yaml", "config/routes/web_profiler.yaml"]}, "twig/extra-bundle": {"version": "v3.4.0"}, "zenstruck/messenger-monitor-bundle": {"version": "v0.5.1"}}