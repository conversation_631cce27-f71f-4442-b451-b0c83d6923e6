image: gitlab.alienor.net:5050/dev-interne/tdb-docker-swarm:dev

default:
  tags:
    - anetdev

cache:
  paths:
    - vendor/
    - assets/vendor/
  key:
    files:
      - composer.lock

stages:
  - build-image
  - deploy

build-image-prod:
  stage: build-image
  image: docker:latest
  variables:
    DOCKER_TLS_CERTDIR: '/certs'
  before_script:
    - mkdir -p $HOME/.docker
    - echo $DOCKER_AUTH_CONFIG > $HOME/.docker/config.json
  script:
    - docker compose -f compose.yaml -f compose.prod.yaml build php
    - echo "Image build successfully"
    - docker compose -f compose.yaml -f compose.prod.yaml push php
    - echo "Image push successfully"
  only:
    - master

deploy-prod:
  image: gitlab.alienor.net:5050/dev-docker/docker-tools
  stage: deploy
  needs: ['build-image-prod']
  environment:
    name: production
    url: https://tdb-swarm.int.alienor.net
  variables:
    GIT_STRATEGY: none
  script:
    - docker-tools update anet-dev web-tdb-swarm_front_web -i gitlab.alienor.net:5050/dev-interne/tdb-docker-swarm:prod --detach
    - docker-tools update anet-dev web-tdb-swarm_scheduler -i gitlab.alienor.net:5050/dev-interne/tdb-docker-swarm:prod --detach
    - docker-tools update anet-dev web-tdb-swarm_consumer -i gitlab.alienor.net:5050/dev-interne/tdb-docker-swarm:prod --detach
  only:
    - master
