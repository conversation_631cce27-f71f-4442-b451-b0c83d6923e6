<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250530123056 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE web_host_configuration ADD COLUMN cluster_name VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE web_host_configuration ADD COLUMN swarmpit_url VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE web_host_configuration ADD COLUMN stack VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE web_host_configuration ADD COLUMN service_name VARCHAR(255) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TEMPORARY TABLE __temp__web_host_configuration AS SELECT id, hostType, ip, location FROM web_host_configuration');
        $this->addSql('DROP TABLE web_host_configuration');
        $this->addSql('CREATE TABLE web_host_configuration (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, hostType VARCHAR(255) NOT NULL, ip VARCHAR(255) DEFAULT NULL, location VARCHAR(255) DEFAULT NULL)');
        $this->addSql('INSERT INTO web_host_configuration (id, hostType, ip, location) SELECT id, hostType, ip, location FROM __temp__web_host_configuration');
        $this->addSql('DROP TABLE __temp__web_host_configuration');
    }
}
