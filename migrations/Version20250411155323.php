<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250411155323 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE web_host (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, configuration_id INTEGER DEFAULT NULL, name VARCHAR(255) DEFAULT NULL, environnement VARCHAR(255) DEFAULT NULL, expected_visibility VARCHAR(255) DEFAULT NULL, gitlab_remote_url VARCHAR(255) DEFAULT NULL, gitlab_active_branch VARCHAR(255) DEFAULT NULL, last_commit_date VARCHAR(255) DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, CONSTRAINT FK_B251E0873F32DD8 FOREIGN KEY (configuration_id) REFERENCES web_host_configuration (id) NOT DEFERRABLE INITIALLY IMMEDIATE)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_B251E0873F32DD8 ON web_host (configuration_id)');
        $this->addSql('CREATE TABLE web_host_configuration (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, hostType VARCHAR(255) NOT NULL, ip VARCHAR(255) DEFAULT NULL, location VARCHAR(255) DEFAULT NULL)');
        $this->addSql('CREATE TABLE web_host_url (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, web_host_id INTEGER NOT NULL, url VARCHAR(255) DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, health_report_status VARCHAR(255) DEFAULT NULL, health_report_website_url VARCHAR(255) DEFAULT NULL, health_report_status_code INTEGER DEFAULT NULL, ssl_report_is_valid BOOLEAN DEFAULT NULL, ssl_report_statuses CLOB DEFAULT NULL --(DC2Type:json)
        , CONSTRAINT FK_1530F3AEF7177F1B FOREIGN KEY (web_host_id) REFERENCES web_host (id) NOT DEFERRABLE INITIALLY IMMEDIATE)');
        $this->addSql('CREATE INDEX IDX_1530F3AEF7177F1B ON web_host_url (web_host_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP TABLE web_host');
        $this->addSql('DROP TABLE web_host_configuration');
        $this->addSql('DROP TABLE web_host_url');
    }
}
