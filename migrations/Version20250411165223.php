<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250411165223 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP TABLE website');
        $this->addSql('CREATE TEMPORARY TABLE __temp__web_host_url AS SELECT id, web_host_id, url, created_at, updated_at, health_report_status, health_report_website_url, health_report_status_code, ssl_report_is_valid, ssl_report_statuses FROM web_host_url');
        $this->addSql('DROP TABLE web_host_url');
        $this->addSql('CREATE TABLE web_host_url (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, web_host_id INTEGER NOT NULL, url VARCHAR(255) DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, health_report_status VARCHAR(255) DEFAULT NULL, health_report_url VARCHAR(255) DEFAULT NULL, health_report_status_code INTEGER DEFAULT NULL, ssl_report_is_valid BOOLEAN DEFAULT NULL, ssl_report_statuses CLOB DEFAULT NULL --(DC2Type:json)
        , CONSTRAINT FK_1530F3AEF7177F1B FOREIGN KEY (web_host_id) REFERENCES web_host (id) ON UPDATE NO ACTION ON DELETE NO ACTION NOT DEFERRABLE INITIALLY IMMEDIATE)');
        $this->addSql('INSERT INTO web_host_url (id, web_host_id, url, created_at, updated_at, health_report_status, health_report_url, health_report_status_code, ssl_report_is_valid, ssl_report_statuses) SELECT id, web_host_id, url, created_at, updated_at, health_report_status, health_report_website_url, health_report_status_code, ssl_report_is_valid, ssl_report_statuses FROM __temp__web_host_url');
        $this->addSql('DROP TABLE __temp__web_host_url');
        $this->addSql('CREATE INDEX IDX_1530F3AEF7177F1B ON web_host_url (web_host_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE website (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, urls CLOB NOT NULL COLLATE "BINARY" --(DC2Type:json)
        , expected_visibility VARCHAR(255) DEFAULT NULL COLLATE "BINARY", environnement VARCHAR(255) DEFAULT NULL COLLATE "BINARY", gitlab_remote_url VARCHAR(255) DEFAULT NULL COLLATE "BINARY", gitlab_active_branch VARCHAR(255) DEFAULT NULL COLLATE "BINARY", last_commit_date VARCHAR(255) DEFAULT NULL COLLATE "BINARY", created_at DATETIME NOT NULL --(DC2Type:datetime_immutable)
        , updated_at DATETIME DEFAULT NULL --(DC2Type:datetime_immutable)
        , health_report_status VARCHAR(255) DEFAULT NULL COLLATE "BINARY", health_report_website_url VARCHAR(255) DEFAULT NULL COLLATE "BINARY", health_report_status_code INTEGER DEFAULT NULL, ssl_report_is_valid BOOLEAN DEFAULT NULL, ssl_report_statuses CLOB DEFAULT NULL COLLATE "BINARY" --(DC2Type:json)
        , server VARCHAR(255) DEFAULT NULL COLLATE "BINARY", project_dir_location VARCHAR(255) DEFAULT NULL COLLATE "BINARY")');
        $this->addSql('CREATE TEMPORARY TABLE __temp__web_host_url AS SELECT id, web_host_id, url, created_at, updated_at, health_report_status, health_report_url, health_report_status_code, ssl_report_is_valid, ssl_report_statuses FROM web_host_url');
        $this->addSql('DROP TABLE web_host_url');
        $this->addSql('CREATE TABLE web_host_url (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, web_host_id INTEGER NOT NULL, url VARCHAR(255) DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, health_report_status VARCHAR(255) DEFAULT NULL, health_report_website_url VARCHAR(255) DEFAULT NULL, health_report_status_code INTEGER DEFAULT NULL, ssl_report_is_valid BOOLEAN DEFAULT NULL, ssl_report_statuses CLOB DEFAULT NULL --(DC2Type:json)
        , CONSTRAINT FK_1530F3AEF7177F1B FOREIGN KEY (web_host_id) REFERENCES web_host (id) NOT DEFERRABLE INITIALLY IMMEDIATE)');
        $this->addSql('INSERT INTO web_host_url (id, web_host_id, url, created_at, updated_at, health_report_status, health_report_website_url, health_report_status_code, ssl_report_is_valid, ssl_report_statuses) SELECT id, web_host_id, url, created_at, updated_at, health_report_status, health_report_url, health_report_status_code, ssl_report_is_valid, ssl_report_statuses FROM __temp__web_host_url');
        $this->addSql('DROP TABLE __temp__web_host_url');
        $this->addSql('CREATE INDEX IDX_1530F3AEF7177F1B ON web_host_url (web_host_id)');
    }
}
