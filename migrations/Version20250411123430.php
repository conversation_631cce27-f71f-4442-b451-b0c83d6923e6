<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250411123430 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE processed_messages (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, run_id INTEGER NOT NULL, attempt SMALLINT NOT NULL, message_type VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, dispatched_at DATETIME NOT NULL --(DC2Type:datetime_immutable)
        , received_at DATETIME NOT NULL --(DC2Type:datetime_immutable)
        , finished_at DATETIME NOT NULL --(DC2Type:datetime_immutable)
        , wait_time BIGINT NOT NULL, handle_time BIGINT NOT NULL, memory_usage INTEGER NOT NULL, transport VARCHAR(255) NOT NULL, tags VARCHAR(255) DEFAULT NULL, failure_type VARCHAR(255) DEFAULT NULL, failure_message CLOB DEFAULT NULL, results CLOB DEFAULT NULL --(DC2Type:json)
        )');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP TABLE processed_messages');
    }
}
