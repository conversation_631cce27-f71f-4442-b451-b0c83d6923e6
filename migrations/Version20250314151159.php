<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250314151159 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE website (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, urls CLOB NOT NULL --(DC2Type:json)
        , expected_visibility VARCHAR(255) NOT NULL, environnement VARCHAR(255) NOT NULL, gitlab_remote_url VARCHAR(255) DEFAULT NULL, gitlab_active_branch VARCHAR(255) DEFAULT NULL, last_commit_date VARCHAR(255) DEFAULT NULL, created_at DATETIME NOT NULL --(DC2Type:datetime_immutable)
        , updated_at DATETIME DEFAULT NULL --(DC2Type:datetime_immutable)
        )');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP TABLE website');
    }
}
