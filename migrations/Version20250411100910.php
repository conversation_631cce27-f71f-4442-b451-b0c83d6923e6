<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250411100910 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TEMPORARY TABLE __temp__website AS SELECT id, urls, expected_visibility, environnement, gitlab_remote_url, gitlab_active_branch, last_commit_date, created_at, updated_at, health_report_status, health_report_website_url, health_report_status_code, ssl_report_is_valid, ssl_report_statuses FROM website');
        $this->addSql('DROP TABLE website');
        $this->addSql('CREATE TABLE website (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, urls CLOB NOT NULL --(DC2Type:json)
        , expected_visibility VARCHAR(255) DEFAULT NULL, environnement VARCHAR(255) DEFAULT NULL, gitlab_remote_url VARCHAR(255) DEFAULT NULL, gitlab_active_branch VARCHAR(255) DEFAULT NULL, last_commit_date VARCHAR(255) DEFAULT NULL, created_at DATETIME NOT NULL --(DC2Type:datetime_immutable)
        , updated_at DATETIME DEFAULT NULL --(DC2Type:datetime_immutable)
        , health_report_status VARCHAR(255) DEFAULT NULL, health_report_website_url VARCHAR(255) DEFAULT NULL, health_report_status_code INTEGER DEFAULT NULL, ssl_report_is_valid BOOLEAN DEFAULT NULL, ssl_report_statuses CLOB DEFAULT NULL --(DC2Type:json)
        , server VARCHAR(255) DEFAULT NULL, project_dir_location VARCHAR(255) DEFAULT NULL)');
        $this->addSql('INSERT INTO website (id, urls, expected_visibility, environnement, gitlab_remote_url, gitlab_active_branch, last_commit_date, created_at, updated_at, health_report_status, health_report_website_url, health_report_status_code, ssl_report_is_valid, ssl_report_statuses) SELECT id, urls, expected_visibility, environnement, gitlab_remote_url, gitlab_active_branch, last_commit_date, created_at, updated_at, health_report_status, health_report_website_url, health_report_status_code, ssl_report_is_valid, ssl_report_statuses FROM __temp__website');
        $this->addSql('DROP TABLE __temp__website');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TEMPORARY TABLE __temp__website AS SELECT id, urls, expected_visibility, environnement, gitlab_remote_url, gitlab_active_branch, last_commit_date, created_at, updated_at, health_report_status, health_report_website_url, health_report_status_code, ssl_report_is_valid, ssl_report_statuses FROM website');
        $this->addSql('DROP TABLE website');
        $this->addSql('CREATE TABLE website (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, urls CLOB NOT NULL --(DC2Type:json)
        , expected_visibility VARCHAR(255) NOT NULL, environnement VARCHAR(255) NOT NULL, gitlab_remote_url VARCHAR(255) DEFAULT NULL, gitlab_active_branch VARCHAR(255) DEFAULT NULL, last_commit_date VARCHAR(255) DEFAULT NULL, created_at DATETIME NOT NULL --(DC2Type:datetime_immutable)
        , updated_at DATETIME DEFAULT NULL --(DC2Type:datetime_immutable)
        , health_report_status VARCHAR(255) DEFAULT NULL, health_report_website_url VARCHAR(255) DEFAULT NULL, health_report_status_code INTEGER DEFAULT NULL, ssl_report_is_valid BOOLEAN DEFAULT NULL, ssl_report_statuses CLOB DEFAULT NULL)');
        $this->addSql('INSERT INTO website (id, urls, expected_visibility, environnement, gitlab_remote_url, gitlab_active_branch, last_commit_date, created_at, updated_at, health_report_status, health_report_website_url, health_report_status_code, ssl_report_is_valid, ssl_report_statuses) SELECT id, urls, expected_visibility, environnement, gitlab_remote_url, gitlab_active_branch, last_commit_date, created_at, updated_at, health_report_status, health_report_website_url, health_report_status_code, ssl_report_is_valid, ssl_report_statuses FROM __temp__website');
        $this->addSql('DROP TABLE __temp__website');
    }
}
