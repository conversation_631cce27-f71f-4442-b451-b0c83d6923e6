<script lang="ts">
	import { run, preventDefault } from 'svelte/legacy';

	import { getService, redeployService } from '../../api';
	import { waitingForUpdates } from '../stores';

	interface Props {
		service: Service;
		showDetails?: any;
		showTags?: any;
	}

	let { service, showDetails = (service) => {}, showTags = (service) => {} }: Props = $props();
	let deploying = $state(false);

	async function redeploy() {
		if (
			confirm(
				`Êtes-vous sur de vouloir rédéployer le service "${service.stack} - ${service.name}" ?`
			)
		) {
			setWaiting('redeploy');
			await redeployService(service.server, service.id);
		}
	}

	async function check() {
		setWaiting('refresh');
		await getService(service.server, service.id);
	}

	function setWaiting(type: 'refresh' | 'redeploy') {
		$waitingForUpdates = [
			{
				type,
				serviceId: service.id,
				serverName: service.server,
				waiting: true
			}
		];
	}

	run(() => {
		deploying = service.status.update === 'updating';
	});
</script>

<div class="text-nowrap text-end">
	<button
		class="btn btn-sm btn-info text-white"
		class:btn-info={!service.redeployedAllowed ||
			(service.redeployedAllowed && service.details !== null)}
		class:btn-secondary={service.redeployedAllowed && service.details === null}
		onclick={() => showDetails(service)}><i class="fa fa-info-circle"></i></button
	>
	<button
		onclick={preventDefault(check)}
		class="btn btn-sm btn-info text-white"
		data-bs-toggle="tooltip"
		title="Forcer la mise à jour les données de la ligne"
		><i class="fa fa-cloud-arrow-down"></i></button
	>
	{#if service.redeployedAllowed}
		<button
			onclick={preventDefault(() => showTags(service))}
			class="btn btn-sm btn-danger"
			data-bs-toggle="tooltip"
			title="Modifier le tag"
			><i class="fa fa-tag"></i>
		</button>
		<button
			onclick={preventDefault(redeploy)}
			class="btn btn-sm btn-danger"
			data-bs-toggle="tooltip"
			title="Redeployer le service"
			><i class="fa fa-refresh" class:fa-spin={deploying}></i>
		</button>
	{/if}
</div>
