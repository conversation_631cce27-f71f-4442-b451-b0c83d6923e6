<script lang="ts">
	import { run } from 'svelte/legacy';

	import Image<PERSON>opyButton from '../ImageCopyButton.svelte';
	import { getImageShortName } from '../../utils';

	interface Props {
		service: Service;
	}

	let { service }: Props = $props();
	let digest = $derived(
		service.repository.imageDigest ? service.repository.imageDigest.slice(-8) : null
	);
	let builtAt: string | null = $state();

	run(() => {
		if (service?.details?.php?.buildDate) {
			builtAt = new Date(service.details.php.buildDate).toLocaleDateString('fr-FR', {
				day: '2-digit',
				month: '2-digit',
				year: '2-digit'
			});
		} else {
			builtAt = null;
		}
	});
</script>

<div class="text-end">
	{getImageShortName(service.repository.image)}
	<ImageCopyButton title={service.repository.image} />
	<br />
	#{digest}
</div>
