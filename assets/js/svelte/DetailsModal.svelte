<script lang="ts">
	import { preventDefault } from 'svelte/legacy';

	import { fade } from 'svelte/transition';
	import TimeAgo from 'javascript-time-ago';
	import fr from 'javascript-time-ago/locale/fr';
	import { <PERSON><PERSON>, Modal, Modal<PERSON><PERSON>, <PERSON><PERSON>ooter, ModalHeader } from '@sveltestrap/sveltestrap';
	import { getComposerUsage, getService } from '../api';
	import DetailsAccordion from './DetailsAccordion.svelte';
	import { mapValues, keyBy, isObject } from 'lodash';
	import SonarQube from './Details/SonarQube.svelte';

	interface Props {
		service: Service;
		isOpen?: boolean;
		toggle?: any;
	}

	let { service, isOpen = $bindable(false), toggle = () => (isOpen = !isOpen) }: Props = $props();

	let details: Details | {} = $state({});
	let loading = $state(false);
	let error = $state(false);

	TimeAgo.addDefaultLocale(fr);
	const timeAgo = new TimeAgo('fr-FR');

	async function fetchDetails(force = false) {
		loading = true;
		error = false;

		if (!service.redeployedAllowed) {
			details = {
				env: mapValues(keyBy(service.variables, 'name'), 'value')
			};
		} else {
			try {
				if (service.details === null || force) {
					const result = await getService(service.server, service.id);
					details = result.details;
				} else {
					details = service.details;
				}
			} catch (e) {
				error = true;
			}
		}

		loading = false;
	}

	// Vérification des dépendances

	let dependencies = $state({});

	async function loadDependencies(dep: ComposerDependency) {
		dependencies[dep.name] = await getComposerUsage(dep);
	}

	async function closeDependencies(dep: ComposerDependency) {
		delete dependencies[dep.name];
		dependencies = dependencies;
	}

	async function onOpening() {
		details = {};
	}

	async function onOpen() {
		await fetchDetails();
		window.location.hash = service.id;
	}

	async function onClose() {
		history.replaceState(null, null, ' ');
	}
</script>

<Modal {isOpen} {toggle} size="xl" on:open={onOpen} on:close={onClose} on:opening={onOpening}>
	<ModalHeader {toggle}>{service.server} | {service.stack} | {service.name}</ModalHeader>
	<ModalBody>
		{#if loading}
			<div class="text-center">
				<div class="spinner-border text-primary" role="status">
					<span class="visually-hidden">Loading...</span>
				</div>
			</div>
		{:else if error}
			<div class="alert alert-danger d-flex align-items-center" role="alert">
				<i class="fa-solid fa-circle-exclamation me-2"></i> Impossible de récupérer les informations
				de ce service
			</div>
		{:else}
			<div in:fade>
				{#if details.date}
					<p class="fw-bold">
						<button
							onclick={preventDefault(() => fetchDetails(true))}
							class="btn btn-sm btn-info text-white me-2"
							data-bs-toggle="tooltip"
							title="Forcer la mise à jour les données de la ligne"
							><i class="fa fa-cloud-arrow-down"></i>
						</button>
						Dernière mise à jour : {timeAgo.format(new Date(details.date))}
					</p>
				{/if}
				<div class="accordion" id="accordionPanelsStayOpenExample">
					{#if details.portail && Object.entries(details.portail).length}
						<DetailsAccordion name="portail" title="Config portail">
							<ul class="list-group">
								{#if details.portail.webserviceUrl}
									<li class="list-group-item">
										<span class="fw-bold">Webservice URL</span> :
										<a target="_blank" href={details.portail.webserviceUrl}
											>{details.portail.webserviceUrl}</a
										>
									</li>
								{/if}
							</ul>
						</DetailsAccordion>
					{/if}
					{#if details.php}
						<DetailsAccordion name="php" title="PHP">
							<ul class="list-group">
								<li class="list-group-item">
									<span class="fw-bold">Version</span> : {details.php.version}
								</li>
								{#if service.links.phpinfo}
									<li class="list-group-item">
										<span class="fw-bold">phpinfo()</span> :
										<a target="_blank" href={service.links.phpinfo}>{service.links.phpinfo}</a>
									</li>
								{/if}
							</ul>
						</DetailsAccordion>
					{/if}
					{#if details.symfony}
						<DetailsAccordion name="symfony" title="Symfony">
							<ul class="list-group">
								<li class="list-group-item">
									<span class="fw-bold">Version</span> : {details.symfony.version}
								</li>
								{#if details.symfony.latest_patch_version}
									<li class="list-group-item">
										<span
											class:fw-bold={details.symfony.latest_patch_version !==
												details.symfony.version}
											class:text-warning={details.symfony.latest_patch_version !==
												details.symfony.version}>Dernière version</span
										>
										: {details.symfony.latest_patch_version}
									</li>
									<li class="list-group-item">
										<span
											class:fw-bold={details.symfony.is_eomed}
											class:text-danger={details.symfony.is_eomed}>Fin de maintenance</span
										>
										: {details.symfony.eom}
									</li>
									<li class="list-group-item">
										<span
											class:fw-bold={details.symfony.is_eoled}
											class:text-danger={details.symfony.is_eoled}>Fin de vie</span
										>
										: {details.symfony.eol}
									</li>
								{/if}
							</ul>
						</DetailsAccordion>
					{/if}
					{#if details.sonarQube}
						<DetailsAccordion name="sonarQube" title="SonarQube">
							<SonarQube {details} />
						</DetailsAccordion>
					{/if}
					{#if details.wordpress}
						<DetailsAccordion name="wordpress" title="Wordpress">
							<ul class="list-group">
								<li class="list-group-item">
									<span class="fw-bold">Version</span> : {details.wordpress.version}
								</li>
							</ul>
						</DetailsAccordion>
					{/if}
					{#if details.htaccess && details.htaccess.exists}
						<DetailsAccordion name="htaccess" title="Htaccess">
							<ul class="list-group">
								{#if details.htaccess.requireUser}
									<li class="list-group-item">
										<span class="fw-bold">Require User</span> : {details.htaccess.requireUser.join(
											', '
										)}
									</li>
								{/if}
							</ul>
						</DetailsAccordion>
					{/if}
					{#if details.git && details.git.url}
						<DetailsAccordion name="git" title="Git">
							<ul class="list-group">
								<li class="list-group-item">
									<span class="fw-bold">URL</span> :
									<a target="_blank" href={details.git.url}>{details.git.url}</a>
								</li>
								<li class="list-group-item">
									<span class="fw-bold">Branche</span> : {details.git.branch}
								</li>
								{#if details.git.tag}
									<li class="list-group-item">
										<span class="fw-bold">Tag</span> : {details.git.tag}
									</li>
								{/if}
								<li class="list-group-item">
									<ul class="list-group">
										{#each details.git.history as commit}
											<li class="list-group-item">
												<a target="_blank" href={commit.url}>#{commit.hash.slice(0, 8)}</a>
												<span class="fw-bold">{commit.date}</span>
												: {commit.message}
											</li>
										{/each}
									</ul>
								</li>
							</ul>
						</DetailsAccordion>
					{/if}
					{#if details.env && Object.entries(details.env).length}
						<DetailsAccordion name="env" title="ENV">
							<ul class="list-group">
								{#each Object.entries(details.env) as e}
									<li class="list-group-item align-items-center d-flex justify-content-between">
										<div><span class="fw-bold">{e[0]}</span> : {e[1]}</div>
										<button
											class="btn btn-sm btn-info text-white"
											onclick={preventDefault(() => {
												navigator.clipboard.writeText(e[1]);
											})}
										>
											<i class="fa fa-copy"></i>
										</button>
									</li>
								{/each}
							</ul>
						</DetailsAccordion>
					{/if}
					{#if details['local-php-security-checker'] && Object.entries(details['local-php-security-checker']).length}
						<DetailsAccordion name="security" title="Security">
							<ul class="list-group">
								{#each Object.entries(details['local-php-security-checker']) as [name, check]}
									<li class="list-group-item">
										<span class="fw-bold">{name}</span> :
									</li>
									<ul class="list-group">
										{#each Object.entries(check) as [packageName, advisories]}
											{@const adv = isObject(advisories) ? Object.values(advisories) : advisories}
											{#each adv as advisory}
												<li class="list-group-item">
													<div>
														<span class="fw-bold">{packageName}</span>
													</div>
													<div>
														<a href={advisory.link} target="_blank">{advisory.link}</a>
													</div>
													<div>{advisory.title}</div>
												</li>
											{/each}
										{/each}
									</ul>
								{/each}
							</ul>
						</DetailsAccordion>
					{/if}
					{#if details.composer}
						<DetailsAccordion name="composer" title="Composer" opened={false}>
							<ul class="list-group">
								{#each details.composer.installed as dep}
									<li class="list-group-item align-items-center d-flex justify-content-between">
										<div>
											<span class="fw-bold" class:text-primary={dep.name.includes('alienor/')}
												>{dep.name}</span
											>
											: {dep.version}
										</div>
										<button
											class="btn btn-sm btn-info text-white"
											onclick={preventDefault(() => {
												if (dependencies[dep.name]) {
													closeDependencies(dep);
												} else {
													loadDependencies(dep);
												}
											})}
										>
											<i class="fa fa-{dependencies[dep.name] ? 'close' : 'magnifying-glass'}"></i>
										</button>
									</li>
									{#if dependencies[dep.name]}
										<li class="list-group-item">
											<ul>
												{#each dependencies[dep.name] as url (url)}
													<li><a target="_blank" href={url}>{url}</a></li>
												{/each}
											</ul>
										</li>
									{/if}
								{/each}
							</ul>
						</DetailsAccordion>
					{/if}
				</div>
			</div>
		{/if}
	</ModalBody>
	<ModalFooter>
		<Button color="secondary" on:click={toggle}>Fermer</Button>
	</ModalFooter>
</Modal>
