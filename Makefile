SUDO=sudo -u www-data
CONSOLE=php bin/console

### COMPOSER ###
composer:
	command -v composer &> /dev/null && $(SUDO) composer install || $(SUDO) php ../composer.phar install

### CONSOLE ###
cache-clear:
	$(SUDO) $(CONSOLE) cache:clear
	$(SUDO) $(CONSOLE) cache:warmup

build:
	$(SUDO) yarn install --force
	$(SUDO) yarn encore production

compile:
	$(SUDO) rm swarm-checker/dist/swarm-checker.phar
	$(SUDO) php -d phar.readonly=off vendor/bin/phar-composer build swarm-checker swarm-checker/dist/

package:
	curl --header "PRIVATE-TOKEN: **************************" --upload-file swarm-checker/dist/swarm-checker.phar "https://gitlab.alienor.net/api/v4/projects/dev-interne%2Ftdb-docker-swarm/packages/generic/swarm-checker/1.0.4/swarm-checker.phar"
	curl --header "PRIVATE-TOKEN: **************************" --upload-file swarm-checker/dist/checker.php "https://gitlab.alienor.net/api/v4/projects/dev-interne%2Ftdb-docker-swarm/packages/generic/swarm-checker/1.0.4/checker.php"