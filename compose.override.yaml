# Development environment override
services:
  php:
    build:
      context: .
      target: frankenphp_dev
    volumes:
      - ./:/var/www/html
      - ./frankenphp/Caddyfile:/etc/caddy/Caddyfile:ro
      - ./frankenphp/conf.d/20-app.dev.ini:/usr/local/etc/php/app.conf.d/20-app.dev.ini:ro
      # If you develop on Mac or Windows you can remove the vendor/ directory
      #  from the bind-mount for better performance by enabling the next line:
      #- /app/vendor
    environment:
      # See https://xdebug.org/docs/all_settings#mode
      XDEBUG_MODE: '${XDEBUG_MODE:-off}'
    extra_hosts:
      # Ensure that host.docker.internal is correctly defined on Linux
      - host.docker.internal:host-gateway
    tty: true

  consumer:
    build:
      context: .
      target: frankenphp_dev
    command: ['php', '/var/www/html/bin/console', 'messenger:consume', 'async', '-vvv']
    healthcheck:
      test: ['CMD', 'echo', 'yes']
      interval: 10s
      timeout: 3s
      retries: 3
      start_period: 30s
    volumes:
      - ./:/var/www/html
      - ./frankenphp/Caddyfile:/etc/caddy/Caddyfile:ro
      - ./frankenphp/conf.d/20-app.dev.ini:/usr/local/etc/php/app.conf.d/20-app.dev.ini:ro
    environment:
      # See https://xdebug.org/docs/all_settings#mode
      XDEBUG_MODE: '${XDEBUG_MODE:-off}'
      MERCURE_URL: 'http://php/.well-known/mercure'
    extra_hosts:
      # Ensure that host.docker.internal is correctly defined on Linux
      - host.docker.internal:host-gateway
    tty: true
